﻿#include "YouRenJiWorker.h"
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonParseError>
#include <QDebug>
#include <QCoreApplication>
#include <QtConcurrent>
#include <QThread>
#include <QDateTime>
#include <QFile>
#include <QFileInfo>
#include <QDir>
#include <QElapsedTimer>
#include <QNetworkDatagram>

YouRenJiWorker::YouRenJiWorker(QObject *parent)
    : QObject(parent)
    , isRunning(false)
    , serialPortOpen(false)
    , messageCount(0)
    , dataBytesProcessed(0)
    , messageSamplingRate(1)  // 默认处理所有消息
    , logSamplingRate(1)      // 默认记录所有日志
    , messageCounter(0)
    , logCounter(0)
    , bufferFlushTimer(nullptr)
    , zaiheUdpSend(nullptr)
    , uavUdpRecv(nullptr)
    , serialPort(nullptr)
{
    // 注册元类型，以支持跨线程信号槽
    qRegisterMetaType<QAbstractSocket::SocketError>("QAbstractSocket::SocketError");
    qRegisterMetaType<QAbstractSocket::SocketState>("QAbstractSocket::SocketState");
    qRegisterMetaType<QSerialPort::SerialPortError>("QSerialPort::SerialPortError");

    // 初始化性能计时器
    performanceTimer.start();

    // 光电赋值
    //    eo_info.control_type = 1;
    //    eo_info.control_order = 1;
    //    eo_info.az_ang = 1;
    //    eo_info.ei_ang = 1;
    //    eo_info.ccd_view = 1;
    //    eo_info.ir_view = 1;
    //    eo_info.photo_view = 1;
    //    eo_info.pod_status_1.spare = 1;
    //    eo_info.pod_status_1.eo_status = 1;
    //    eo_info.pod_status_1.camera_status = 1;
    //    eo_info.pod_status_1.ir_sensor_status = 1;
    //    eo_info.pod_status_1.tv_sensor_status = 1;
    //    eo_info.pod_status_1.servo_unit_status = 1;
    //    eo_info.pod_status_1.image_track_status = 1;
    //    eo_info.pod_status_1.control_unit_status = 1;
    //    eo_info.pod_status_two.spare = 1;
    //    eo_info.pod_status_two.in_pitch_status = 1;
    //    eo_info.pod_status_two.out_pitch_status = 1;
    //    eo_info.pod_status_two.pitch_top_status = 1;
    //    eo_info.pod_status_two.in_postion_status = 1;
    //    eo_info.pod_status_two.out_postion_status = 1;
    //    eo_info.pod_status_two.postion_top_status = 1;
    //    eo_info.pod_status_two.sensor_control_status = 1;
    //    eo_info.pod_work_status.ir_statu = 1;
    //    eo_info.pod_work_status.camare_statu = 1;
    //    eo_info.pod_work_status.ir_big_statu = 1;
    //    eo_info.pod_work_status.ir_mix_statu = 1;
    //    eo_info.pod_work_status.track_num_statu = 1;
    //    eo_info.pod_work_status.ir_enhance_statu = 1;
    //    eo_info.pod_work_status.image_track_statu = 1;
    //    eo_info.pod_work_status.ir_trans_fog_statu = 1;
    //    eo_info.pod_work_status.ir_right_image_statu = 1;
    //    eo_info.pod_work_status_Two.spare = 1;
    //    eo_info.pod_work_status_Two.ir_main_path = 1;
    //    eo_info.pod_work_status_Two.eo_work_status = 1;
    //    eo_info.pod_work_status_Two.search_condition = 1;
    //    eo_info.pod_work_status_Two.class_width_statu = 1;
    //    eo_info.pod_work_status_Two.image_track_statu = 1;
    //    eo_info.pod_work_status_Two.stick_sensitive_statu = 1;
    memset(&eo_info,0,sizeof (eo_info));
}

YouRenJiWorker::~YouRenJiWorker()
{
    stop();

    if (bufferFlushTimer) {
        bufferFlushTimer->stop();
        delete bufferFlushTimer;
        bufferFlushTimer = nullptr;
    }

    // 清理Socket对象
    delete zaiheUdpSend;
    delete uavUdpRecv;

    // 清理SerialPort对象
    if (serialPort) {
        if (serialPort->isOpen()) {
            serialPort->close();
        }
        delete serialPort;
        serialPort = nullptr;
    }
}

// 辅助方法，添加时间戳并发送日志
void YouRenJiWorker::emitLogMessage(const QString &message)
{
    QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
    emit messageReceived(QString("[%1] %2").arg(timestamp).arg(message));
}

// 辅助方法，添加时间戳并发送错误
void YouRenJiWorker::emitErrorMessage(const QString &message)
{
    QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
    emit errorOccurred(QString("[%1] %2").arg(timestamp).arg(message));
}

// 初始化UDP Socket
void YouRenJiWorker::initUdpSockets()
{
    // 创建Socket对象
    zaiheUdpSend = new QUdpSocket(this);
    uavUdpRecv = new QUdpSocket(this);

    // 设置缓冲区大小以处理高吞吐量
    const int bufferSize = 16 * 1024 * 1024; // 16MB缓冲区
    zaiheUdpSend->setSocketOption(QAbstractSocket::SendBufferSizeSocketOption, bufferSize);
    uavUdpRecv->setSocketOption(QAbstractSocket::ReceiveBufferSizeSocketOption, bufferSize);

    // 连接信号
    connect(uavUdpRecv, &QUdpSocket::readyRead, this, &YouRenJiWorker::processUavUdpData);
    //    connect(this, &YouRenJiWorker::startUDP, this, &YouRenJiWorker::processUavUdpData);

    // 绑定UAV MultiCast接收端口并加入组
    try {
        if (!uavUdpRecv->bind(QHostAddress::AnyIPv4, uavRecvPort.toUShort(), QUdpSocket::ShareAddress | QUdpSocket::ReuseAddressHint)) {
            throw std::runtime_error(uavUdpRecv->errorString().toStdString());
        }

        if (!uavUdpRecv->joinMulticastGroup(QHostAddress(uavRecvIP))) {
            throw std::runtime_error(uavUdpRecv->errorString().toStdString());
        }

        emitLogMessage(QString("[系统] 无人机接收组播绑定成功: %1:%2").arg(uavRecvIP).arg(uavRecvPort));
    } catch (const std::exception& e) {
        emitErrorMessage(QString("[错误] 无人机接收组播绑定失败: %1").arg(e.what()));
    }
}

// 初始化串口
void YouRenJiWorker::initSerialPort()
{
    if (serialPort == nullptr) {
        serialPort = new QSerialPort(this);

        // 连接信号
        connect(serialPort, &QSerialPort::errorOccurred, this, [this](QSerialPort::SerialPortError error) {
            if (error != QSerialPort::NoError) {
                emit serialErrorOccurred(serialPort->errorString());
                // 如果是严重错误，关闭串口
                if (error != QSerialPort::TimeoutError && error != QSerialPort::NotOpenError) {
                    stopSerialPort();
                }
            }
        });

        // 连接readyRead信号到处理串口数据的方法
//        connect(serialPort, &QSerialPort::readyRead, this, &YouRenJiWorker::processSerialData);
                connect(this, &YouRenJiWorker::startSerial, this, &YouRenJiWorker::processSerialData);
    }
}

void YouRenJiWorker::setAddresses(
        const QString &zaiheSendIP, const QString &zaiheSendPort,
        const QString &uavRecvIP, const QString &uavRecvPort)
{
    QMutexLocker locker(&stateMutex);
    this->zaiheSendIP = zaiheSendIP;
    this->zaiheSendPort = zaiheSendPort;
    this->uavRecvIP = uavRecvIP;
    this->uavRecvPort = uavRecvPort;
}

void YouRenJiWorker::setSerialPortSettings(
        const QString &portName,
        int baudRate,
        QSerialPort::DataBits dataBits,
        QSerialPort::Parity parity,
        QSerialPort::StopBits stopBits)
{
    QMutexLocker locker(&stateMutex);
    this->serialPortName = portName;
    this->serialBaudRate = baudRate;
    this->serialDataBits = dataBits;
    this->serialParity = parity;
    this->serialStopBits = stopBits;

    emitLogMessage(QString("[系统] 串口设置已更新: %1, %2波特, %3数据位, %4校验位, %5停止位")
                   .arg(portName)
                   .arg(baudRate)
                   .arg(dataBits)
                   .arg(parity)
                   .arg(stopBits));
}

void YouRenJiWorker::setMessageSamplingRate(int rate)
{
    QMutexLocker locker(&stateMutex);
    messageSamplingRate = qMax(1, rate); // 确保至少是1
    emitLogMessage(QString("[系统] 消息采样率已设置为 1/%1").arg(messageSamplingRate));
}

void YouRenJiWorker::setLogSamplingRate(int rate)
{
    QMutexLocker locker(&stateMutex);
    logSamplingRate = qMax(1, rate); // 确保至少是1
    emitLogMessage(QString("[系统] 日志采样率已设置为 1/%1").arg(logSamplingRate));
}

bool YouRenJiWorker::shouldLogMessage()
{
    return (++logCounter % logSamplingRate) == 0;
}

void YouRenJiWorker::start()
{
    if (!isRunning) {
        QMutexLocker locker(&stateMutex);
        serialAlign = false;
        isRunning = true;
        emitLogMessage("[系统] 正在启动消息处理系统...");
        // 初始化UDP Sockets
        initUdpSockets();
        // 初始化定时器
        if (!bufferFlushTimer) {
            bufferFlushTimer = new QTimer(this);
            bufferFlushTimer->setInterval(BUFFER_FLUSH_INTERVAL_MS);
            connect(bufferFlushTimer, &QTimer::timeout, this, &YouRenJiWorker::processSerialData);
            //            connect(this, &YouRenJiWorker::startBuffer, this, &YouRenJiWorker::flushBufferedData);
        }

        //        createSubThread(&fun1, &helper1, "fun1");
        //        createSubThread(&fun2, &helper2, "fun2");
        //        createSubThread(&fun3, &helper3, "fun3");
        //        QMetaObject::invokeMethod(helper1, "trigger", Qt::QueuedConnection);
        //        QMetaObject::invokeMethod(helper2, "trigger", Qt::QueuedConnection);
        //        QMetaObject::invokeMethod(helper3, "trigger", Qt::QueuedConnection);



        // 设置线程优先级
        QThread::currentThread()->setPriority(QThread::HighPriority);

        // 重置计数器
        messageCount = 0;
        dataBytesProcessed = 0;

        bufferFlushTimer->start();

        emit connectionStatusChanged(true);
        emitLogMessage("[系统] 消息处理系统已启动");
    }
}

void YouRenJiWorker::stop()
{
    if (isRunning) {
        QMutexLocker locker(&stateMutex);
        isRunning = false;
        serialAlign = false;
        emitLogMessage("[系统] 正在停止消息处理系统...");
        stopSubThreads();
        if (bufferFlushTimer) {
            bufferFlushTimer->stop();
        }

        // 清空所有缓冲区
        QMutexLocker bufferLocker(&bufferMutex);
        serialDataQueue.clear();

        emit connectionStatusChanged(false);
        emitLogMessage("[系统] 消息处理系统已停止");
    }
}

void YouRenJiWorker::startSerialPort()
{
    QMutexLocker locker(&stateMutex);

    // 如果已经打开，先关闭
    if (serialPort && serialPort->isOpen()) {
        serialPort->close();
        emitLogMessage("[系统] 关闭之前的串口连接");
    }

    // 如果没有串口对象，创建一个
    if (!serialPort) {
        initSerialPort();
    }

    // 配置串口参数
    serialPort->setPortName(serialPortName);
    serialPort->setBaudRate(serialBaudRate);
    serialPort->setDataBits(serialDataBits);
    serialPort->setParity(serialParity);
    serialPort->setStopBits(serialStopBits);
    serialPort->setFlowControl(QSerialPort::NoFlowControl);

    // 尝试打开串口
    if (serialPort->open(QIODevice::ReadWrite)) {
        serialPortOpen = true;
        emit serialPortStatusChanged(true);
        emitLogMessage(QString("[系统] 串口 %1 已成功打开").arg(serialPortName));
    } else {
        serialPortOpen = false;
        emit serialPortStatusChanged(false);
        emitErrorMessage(QString("[错误] 无法打开串口 %1: %2").arg(serialPortName).arg(serialPort->errorString()));
    }
}

void YouRenJiWorker::stopSerialPort()
{
    QMutexLocker locker(&stateMutex);

    if (serialPort && serialPort->isOpen()) {
        serialPort->close();
        serialPortOpen = false;
        emit serialPortStatusChanged(false);
        emitLogMessage(QString("[系统] 串口 %1 已关闭").arg(serialPortName));
    }
}

void YouRenJiWorker::flushBufferedData()
{
    //    qDebug("flushBufferedData");
    QMutexLocker locker(&bufferMutex);
    // 处理并发送队列中的串口数据
    if (!serialDataQueue.isEmpty() && serialPortOpen && serialPort && serialPort->isOpen()) {
        QByteArray data = serialDataQueue.dequeue();
        if(data.size() == 20){
            memcpy(&eo_info, data.data(), sizeof (eo_info));
        }else if (data.size() == 256){
            int chunkSize = 64; // 每份 64 字节
            int totalChunks = data.size() / chunkSize; // 数据被拆分成多少个完整块
            if (data.size() % chunkSize != 0) {
                totalChunks += 1; // 如果有剩余字节，需要额外添加一个块
            }
            // 按 64 字节分块发送数据
            for (int i = 0; i < totalChunks; ++i) {
                int offset = i * chunkSize;
                int currentChunkSize = (i == totalChunks - 1) ? data.size() - offset : chunkSize; // 最后一块数据可能不足 64 字节

                QByteArray chunkData = data.mid(offset, currentChunkSize); // 获取当前分块数据
                if(i == 3){
                    chunkData[3] = 0x53;
                    memcpy((chunkData.data() + 5),&eo_info,sizeof (eo_info));
                    short j = 0;
                    for (int k = 3;k< 62;k++) {
                        j += chunkData[k];
                    }
                    chunkData[63] = j;
                }
                // 写入数据到串口
                if (serialPort->write(chunkData) != currentChunkSize) {
                    emitErrorMessage(QString("[错误] 串口数据发送不完整: %1").arg(serialPort->errorString()));
                    break;
                }

                // 确保数据立即发送
                serialPort->flush();
                //                emit startSerial();
            }

        }
    }

}

// 处理串口接收到的数据并转发到载荷组播
void YouRenJiWorker::processSerialData()
{
    //    qDebug("processSerialData");
    if (!isRunning || !serialPort || !serialPort->isOpen()) return;
    QByteArray serialData, tmpData;
    uint8_t type, type1, cmdType, cmdCode, cmdData;
    int alignCnt = 62;
    //    QMutexLocker locker(&stateMutex);
//    if(!serialAlign){
        do{
            header1 = serialPort->read(1);
            if(static_cast<unsigned char>(header1[0]) == 0xeb){
                header2 = serialPort->read(1);
                if(static_cast<unsigned char>(header2[0]) == 0x90){

                    while(alignCnt--){
                        tmpData.append(serialPort->read(1));
                    }
                    break;
                }
            }
            else if(static_cast<unsigned char>(header1[0]) == 0xaa){
                header2 = serialPort->read(1);
                if(static_cast<unsigned char>(header2[0]) == 0x55){
                    while(alignCnt--){
                        tmpData.append(serialPort->read(1));
                    }
                    break;
                }
            }
            if (alignCnt == 0) break;
        } while(1);

        serialData.append(header1);
        serialData.append(header2);
        serialData.append(tmpData);
//    } else {
//        serialData = serialPort->read(64);
//    }
    if(static_cast<unsigned char>(serialData[0]) == 0xeb){
        type = static_cast<unsigned char>(serialData[5]);
        type1 = static_cast<unsigned char>(serialData[3]);
        if (type == 0xA3 && type1 == 0x5A) {
                    uint8_t tmp = static_cast<unsigned char>(serialData[17]);
                    uint8_t tmp1 = static_cast<unsigned char>(serialData[18]);
                    uint8_t tmp2 = static_cast<unsigned char>(serialData[19]);
                    if(tmp==0x33 && tmp1==0x33 && tmp2 == 0x33){
                        qDebug() << "received it";
                    }
            // 直接转发原始数据
            // uavUdpRecv->writeDatagram(serialData, QHostAddress(uavRecvIP), uavRecvPort.toUShort());
            uavUdpRecv->writeDatagram(serialData, QHostAddress("**********"), 7101);
        }

    } else if(static_cast<unsigned char>(serialData[0]) == 0xaa){
        cmdType = static_cast<unsigned char>(serialData[9]);
        cmdCode = static_cast<unsigned char>(serialData[10]);
        cmdData = static_cast<unsigned char>(serialData[11]);
        if (cmdType == 0x20 && cmdCode == 0x30) {
            emitLogMessage(QString("手控指令接收成功"));
            send_YK(CONTROL, 0, 0, 0, 0, 0);
        }
        else if (cmdType == 0x20 && cmdCode == 0x32)
        {
            emitLogMessage(QString("自检指令接收成功"));
            send_YK(SELFINSPECTION, 0, 0, 0, 0, 0);
        }
        else if (cmdType == 0x20 && cmdCode == 0x34)
        {
            emitLogMessage(QString("归零指令接收成功"));
            send_YK(RETURNZERO, 0, 0, 0, 0, 0);
        }
        else if (cmdType == 0x20 && cmdCode == 0xB4)
        {
            emitLogMessage(QString("拍照指令接收成功"));
            send_YK(TAKEPHOTO,0,0,0,0,0);
        }
        else if (cmdType == 0x20 && cmdCode == 0x40)
        {
            emitLogMessage(QString("相机垂直下视指令接收成功"));
            send_YK(RETURNVIEWSTATE, 0, 0, 0, 0, 0);
        }
        else if (cmdType == 0x20 && cmdCode == 0x7A && cmdData == 0x01)
        {
            emitLogMessage(QString("视景上移指令接收成功"));
            send_YK(VIEWUP,0, 0, 0, 0, 0);
        }
        else if (cmdType == 0x20 && cmdCode == 0x7A && cmdData == 0xFF)
        {
            emitLogMessage(QString("视景下移指令接收成功"));
            send_YK(VIEWDOWN,0, 0, 0, 0, 0);
        }
        else if (cmdType == 0x20 && cmdCode == 0x7C && cmdData == 0xFF)
        {
            emitLogMessage(QString("视景左移指令接收成功"));
            send_YK(VIEWLEFT,0, 0, 0, 0, 0);
        }
        else if (cmdType == 0x20 && cmdCode == 0x7C && cmdData == 0x01)
        {
            emitLogMessage(QString("视景右移指令接收成功"));
            send_YK(VIEWRIGHT,0, 0, 0, 0, 0);
        }
        else if (cmdType == 0x20 && cmdCode == 0x42)
        {
            emitLogMessage(QString("跟踪指令接收成功"));
            send_YK(TARGETTRACK, 0, 0, 0, 0, 0);
        }
        else if (cmdType == 0x20 && cmdCode == 0x44)
        {
            emitLogMessage(QString("位置指令接收成功"));
            send_YK(POSITIONTRACK, 0, 0, 0, 0, 0);
        }
        else if (cmdType == 0x20 && cmdCode == 0x46)
        {
            if (cmdData == 0x00)
            {
                send_YK(SWITCHLOAD, 0, 0, 0, 0, 0);//0：电视
            }
            else if (cmdData == 0x01)
            {
                send_YK(SWITCHLOAD, 1, 0, 0, 0, 0);//1：红外
            }
            emitLogMessage(QString("跟踪监视主通道指令接收成功"));
        }
        else if (cmdType == 0x20 && cmdCode == 0x80)
        {
            if (cmdData == 0x00)
            {
                send_YK(CCDCHANGEZOOM, 0, 0, 0, 0, 0);//0：电视变焦停
            }
            else if (cmdData == 0x01)
            {
                send_YK(CCDCHANGEZOOM, 1, 0, 0, 0, 0);//1：电视变焦+
            }
            else if (cmdData == 0xFF)
            {
                send_YK(CCDCHANGEZOOM, 2, 0, 0, 0, 0);//1：电视变焦-
            }
            emitLogMessage(QString("电视变焦指令接收成功"));
        }
        else if (cmdType == 0x20 && cmdCode == 0x98)
        {
            if (cmdData == 0x00)
            {
                send_YK(IRCHANGEZOOM, 0, 0, 0, 0, 0);//0：电视变焦停
            }
            else if (cmdData == 0x01)
            {
                send_YK(IRCHANGEZOOM, 1, 0, 0, 0, 0);//1：电视变焦+
            }
            else if (cmdData == 0xFF)
            {
                send_YK(IRCHANGEZOOM, 2, 0, 0, 0, 0);//1：电视变焦-
            }
            emitLogMessage(QString("红外变焦指令接收成功"));
        }
        else if (cmdType == 0x20 && cmdCode == 0x98)
        {
            if (cmdData == 0x01)
            {
                send_YK(CHANGESENSITIVITY, 0, 0, 0, 0, 0);//0：灵敏度+（共6个挡位）
            }
            else if (cmdData == 0xFF)
            {
                send_YK(CHANGESENSITIVITY, 1, 0, 0, 0, 0);//1：灵敏度-（共6个挡位）
            }
            emitLogMessage(QString("灵敏度指令接收成功"));
        }
        else if (cmdType == 0x20 && cmdCode == 0x52)
        {
            emitLogMessage(QString("光电载荷关机指令接收成功"));
            send_YK(CCDCLOSE, 0, 0, 0, 0, 0);//1：灵敏度-（共6个挡位）
        }
        else if (cmdType == 0x20 && cmdCode == 0x10)
        {
            emitLogMessage(QString("光电载荷唤醒指令接收成功"));
            send_YK(CCDAWAKEN, 0, 0, 0, 0, 0);//1：灵敏度-（共6个挡位）
        }
        else if (cmdType == 0x20 && cmdCode == 0x90)
        {
            emitLogMessage(QString("红外电源开指令接收成功"));
            send_YK(IROPEN, 0, 0, 0, 0, 0);//1：灵敏度-（共6个挡位）
        }
        else if (cmdType == 0x20 && cmdCode == 0x92)
        {
            emitLogMessage(QString("红外电源关指令接收成功"));
            send_YK(IRCLOSE, 0, 0, 0, 0, 0);//1：灵敏度-（共6个挡位）
        }
        else if (cmdType == 0x20 && cmdCode == 0xB6)
        {
            emitLogMessage(QString("连续拍照指令接收成功"));
            send_YK(IRCLOSE, cmdData, 0, 0, 0, 0);//1：灵敏度-（共6个挡位）
        }
    }

    // 更新统计信息
    messageCount++;
    dataBytesProcessed += serialData.size();

    // 记录日志
    if (shouldLogMessage()) {
        QString hexData = serialData.toHex();
        // 限制日志长度以避免UI过载
        if (hexData.length() > 200) {
            hexData = hexData.left(197) + "...";
        }
        emitLogMessage(QString("[串口=>载荷组播] <span class='serial'>%1</span>").arg(hexData));
    }

}

void YouRenJiWorker::send_YK(const int& cmdNum,const double&cmdValue1, const double& cmdValue2, const double& cmdValue3, const double& cmdValue4, const double& cmdValue5)
{
    // 构建JSON数据
    QJsonObject json;

    json["MESSAGETYPE"] = "YKCmd";
    json["cmdType"] = cmdNum;
    json["cmdValue1"] = cmdValue1;
    json["cmdValue2"] = cmdValue2;
    json["cmdValue3"] = cmdValue3;
    json["cmdValue4"] = cmdValue4;
    json["cmdValue5"] = cmdValue5;

    QJsonDocument doc(json);
    QByteArray jsonData = doc.toJson();

    // 发送JSON数据
    zaiheUdpSend->writeDatagram(jsonData, QHostAddress("*********"), 6604);
}

// 处理无人机UDP数据
void YouRenJiWorker::processUavUdpData()
{
    //    qDebug("processUavUdpData");
    if (!isRunning || !serialPortOpen) return;
    //    QMutexLocker locker(&stateMutex);
    if (uavUdpRecv->hasPendingDatagrams()) {
        // 消息采样，只处理部分消息以提高性能
//        if ((++messageCounter % messageSamplingRate) != 0) {
//            // 跳过这条消息，但仍然要读取出来以清空缓冲区
//            QByteArray datagram;
//            datagram.resize(uavUdpRecv->pendingDatagramSize());
//            QHostAddress sender;
//            quint16 senderPort;
//            uavUdpRecv->readDatagram(datagram.data(), datagram.size(), &sender, &senderPort);
//            //            continue;
//        }

        // 读取UDP数据包

        QNetworkDatagram A = uavUdpRecv->receiveDatagram();
        QByteArray data = A.data();

        if(data.size() == 20){
            memcpy(&eo_info, data.data(), sizeof (eo_info));
        }else if (data.size() == 256){
            int chunkSize = 64; // 每份 64 字节
            int totalChunks = data.size() / chunkSize; // 数据被拆分成多少个完整块
            if (data.size() % chunkSize != 0) {
                totalChunks += 1; // 如果有剩余字节，需要额外添加一个块
            }
            // 按 64 字节分块发送数据
            for (int i = 0; i < totalChunks; ++i) {
                int offset = i * chunkSize;
                int currentChunkSize = (i == totalChunks - 1) ? data.size() - offset : chunkSize; // 最后一块数据可能不足 64 字节

                QByteArray chunkData = data.mid(offset, currentChunkSize); // 获取当前分块数据
//                if(i == 3){
//                    chunkData[3] = 0x53;
//                    memcpy((chunkData.data() + 5),&eo_info,sizeof (eo_info));
//                    char j = 0;
//                    for (int k = 3;k< 62;k++) {
//                        j += chunkData[k];
//                    }
//                    chunkData[63] = j;
//                }
                // 写入数据到串口
                if (serialPort->write(chunkData) != currentChunkSize) {
                    emitErrorMessage(QString("[错误] 串口数据发送不完整: %1").arg(serialPort->errorString()));
                    break;
                }

                // 确保数据立即发送
                serialPort->flush();
                //                emit startSerial();
            }

        }

        // 更新统计信息
        messageCount++;
        dataBytesProcessed += data.size();

        // 通过串口发送数据
        //sendToSerial(datagram);
        //        serialDataQueue.enqueue(datagram);
        // 记录日志
        if (shouldLogMessage()) {
            QString hexData = data.toHex();
            // 限制日志长度以避免UI过载
            if (hexData.length() > 200) {
                hexData = hexData.left(197) + "...";
            }
            emitLogMessage(QString("[无人机=>串口] <span class='serial'>%1</span>").arg(hexData));
        }

    }
//        emit startSerial();
}

// 发送数据到串口
void YouRenJiWorker::sendToSerial(const QByteArray &data)
{
    if (!serialPortOpen || !serialPort || !serialPort->isOpen()) {
        if (shouldLogMessage()) {
            emitErrorMessage("[错误] 尝试发送数据到未打开的串口");
        }
        return;
    }

    QMutexLocker locker(&bufferMutex);

    // 如果缓冲区已满，移除最早的项目
    if (serialDataQueue.size() >= MAX_SERIAL_BUFFER_SIZE) {
        serialDataQueue.dequeue();
        if (shouldLogMessage()) {
            emitErrorMessage("[警告] 串口发送缓冲区已满，丢弃最早的数据");
        }
    }

    // 将数据加入缓冲区队列
    serialDataQueue.enqueue(data);
}
